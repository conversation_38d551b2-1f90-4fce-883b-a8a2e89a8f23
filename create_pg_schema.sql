-- PostgreSQL数据库创建脚本
-- 创建用户、schema和权限设置

-- 1. 创建用户 agent，密码为 agent@2025
CREATE USER agent WITH PASSWORD 'agent@2025';

-- 2. 创建schema agent
CREATE SCHEMA IF NOT EXISTS agent;

-- 3. 设置用户agent为schema agent的所有者
ALTER SCHEMA agent OWNER TO agent;

-- 4. 授予用户agent对schema agent的所有权限
GRANT ALL PRIVILEGES ON SCHEMA agent TO agent;

-- 5. 授予用户agent在schema agent中创建对象的权限
GRANT CREATE ON SCHEMA agent TO agent;

-- 6. 设置用户agent的默认schema为agent
ALTER USER agent SET search_path TO agent, public;

-- 7. 授予用户agent对未来在schema agent中创建的表的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA agent GRANT ALL ON TABLES TO agent;
ALTER DEFAULT PRIVILEGES IN SCHEMA agent GRANT ALL ON SEQUENCES TO agent;
ALTER DEFAULT PRIVILEGES IN SCHEMA agent GRANT ALL ON FUNCTIONS TO agent;

-- 8. 如果需要，可以授予用户连接数据库的权限（根据具体数据库名称调整）
-- GRANT CONNECT ON DATABASE your_database_name TO agent;

-- 验证创建结果的查询语句
-- 查看用户
-- SELECT usename FROM pg_user WHERE usename = 'agent';

-- 查看schema
-- SELECT schema_name, schema_owner FROM information_schema.schemata WHERE schema_name = 'agent';

-- 查看用户权限
-- SELECT grantee, privilege_type FROM information_schema.schema_privileges WHERE schema_name = 'agent';
